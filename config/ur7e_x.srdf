<?xml version="1.0" encoding="UTF-8"?>
<!--This does not replace URDF, and is not an extension of URDF.
    This is a format for representing semantic information about the robot structure.
    A URDF file must exist for this robot as well, where the joints and the links that are referenced are defined
-->
<robot name="ur7e_x">
    <!--GROUPS: Representation of a set of joints and links. This can be useful for specifying DOF to plan for, defining arms, end effectors, etc-->
    <!--LINKS: When a link is specified, the parent joint of that link (if it exists) is automatically included-->
    <!--JOINTS: When a joint is specified, the child link of that joint (which will always exist) is automatically included-->
    <!--CHAINS: When a chain is specified, all the links along the chain (including endpoints) are included in the group. Additionally, all the joints that are parents to included links are also included. This means that joints along the chain and the parent joint of the base link are included in the group-->
    <!--SUBGROUPS: Groups can also be formed by referencing to already defined group names-->
    <group name="arm_manipulator">
        <chain base_link="root" tip_link="W3J_link"/>
    </group>
    <!--GROUP STATES: Purpose: Define a named state for a particular group, in terms of joint values. This is useful to define states like 'folded arms'-->
    <group_state name="HOME" group="arm_manipulator">
        <joint name="BJ_link_joint" value="0"/>
        <joint name="EJ_link_joint" value="0"/>
        <joint name="SJ_link_joint" value="0"/>
        <joint name="W1J_link_joint" value="0"/>
        <joint name="W2J_link_joint" value="0"/>
        <joint name="W3J_link_joint" value="0"/>
    </group_state>
    <group_state name="FORWARD" group="arm_manipulator">
        <joint name="BJ_link_joint" value="-1.4609"/>
        <joint name="EJ_link_joint" value="1.8191"/>
        <joint name="SJ_link_joint" value="1.125"/>
        <joint name="W1J_link_joint" value="2.0318"/>
        <joint name="W2J_link_joint" value="1.2146"/>
        <joint name="W3J_link_joint" value="0"/>
    </group_state>
</robot>
