<package>

  <name>ur7e_0808</name>
  <version>0.3.0</version>
  <description>
     An automatically generated package with all the configuration and launch files for using the ur7e_x with the MoveIt Motion Planning Framework
  </description>
  <author email="<EMAIL>">li</author>
  <maintainer email="<EMAIL>">li</maintainer>

  <license>BSD</license>

  <url type="website">http://moveit.ros.org/</url>
  <url type="bugtracker">https://github.com/moveit/moveit/issues</url>
  <url type="repository">https://github.com/moveit/moveit</url>

  <buildtool_depend>catkin</buildtool_depend>

  <run_depend>moveit_ros_move_group</run_depend>
  <run_depend>moveit_fake_controller_manager</run_depend>
  <run_depend>moveit_kinematics</run_depend>
  <run_depend>moveit_planners</run_depend>
  <run_depend>moveit_ros_visualization</run_depend>
  <run_depend>moveit_setup_assistant</run_depend>
  <run_depend>moveit_simple_controller_manager</run_depend>
  <run_depend>joint_state_publisher</run_depend>
  <run_depend>joint_state_publisher_gui</run_depend>
  <run_depend>robot_state_publisher</run_depend>
  <run_depend>rviz</run_depend>
  <run_depend>tf2_ros</run_depend>
  <run_depend>xacro</run_depend>
  <!-- The next 2 packages are required for the gazebo simulation.
       We don't include them by default to prevent installing gazebo and all its dependencies. -->
  <!-- <run_depend>joint_trajectory_controller</run_depend> -->
  <!-- <run_depend>gazebo_ros_control</run_depend> -->
  <!-- This package is referenced in the warehouse launch files, but does not build out of the box at the moment. Commented the dependency until this works. -->
  <!-- <run_depend>warehouse_ros_mongo</run_depend> -->
  <run_depend>ur7e_pkg</run_depend>


</package>
