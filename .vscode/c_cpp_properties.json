{"configurations": [{"browse": {"databaseFilename": "${default}", "limitSymbolsToIncludedHeaders": false}, "includePath": ["/home/<USER>/catkin_ws/devel/include/**", "/opt/ros/noetic/include/**", "/home/<USER>/catkin_ws/src/6-dof-robotic-arm-main/owr_gripper_ikfast_arm_manipulator_plugin/include/**", "/home/<USER>/catkin_ws/src/probot_grasping/include/**", "/home/<USER>/catkin_ws/src/ss_gui/include/**", "/home/<USER>/catkin_ws/src/trajectory_planning_gui/include/**", "/home/<USER>/catkin_ws/src/ur7e_pkg/include/**", "/usr/include/**"], "name": "ROS", "intelliSenseMode": "gcc-x64", "compilerPath": "/usr/bin/gcc", "cStandard": "gnu11", "cppStandard": "c++14"}], "version": 4}