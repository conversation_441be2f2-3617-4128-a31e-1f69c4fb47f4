<launch>

  <!-- The request adapters (plugins) used when planning with OMPL. ORDER MATTERS! -->
  <arg name="planning_adapters"
       default="default_planner_request_adapters/LimitMaxCartesianLinkSpeed
                default_planner_request_adapters/AddTimeParameterization
                default_planner_request_adapters/ResolveConstraintFrames
                default_planner_request_adapters/FixWorkspaceBounds
                default_planner_request_adapters/FixStartStateBounds
                default_planner_request_adapters/FixStartStateCollision
                default_planner_request_adapters/FixStartStatePathConstraints"
                />

  <arg name="start_state_max_bounds_error" default="0.1" />
  <arg name="jiggle_fraction" default="0.05" />

  <param name="planning_plugin" value="ompl_interface/OMPLPlanner" />
  <param name="request_adapters" value="$(arg planning_adapters)" />
  <param name="start_state_max_bounds_error" value="$(arg start_state_max_bounds_error)" />
  <param name="jiggle_fraction" value="$(arg jiggle_fraction)" />

  <rosparam command="load" file="$(find ur7e_0808)/config/ompl_planning.yaml"/>

</launch>
