<?xml version="1.0"?>
<launch>
  <!-- MoveIt options -->
  <arg name="pipeline" default="ompl" doc="Planning pipeline to use with MoveIt"/>

  <!-- Gazebo options -->
  <arg name="gazebo_gui" default="true" doc="Start Gazebo GUI"/>
  <arg name="paused" default="false" doc="Start Gazebo paused"/>
  <arg name="world_name" default="worlds/empty.world" doc="Gazebo world file"/>
  <arg name="world_pose" default="-x 0 -y 0 -z 0 -R 0 -P 0 -Y 0" doc="Pose to spawn the robot at"/>

  <!-- Launch Gazebo and spawn the robot -->
  <include file="$(dirname)/gazebo.launch" pass_all_args="true"/>

  <!-- Launch MoveIt -->
  <include file="$(dirname)/demo.launch" pass_all_args="true">
    <!-- robot_description is loaded by gazebo.launch, to enable Gazebo features -->
    <arg name="load_robot_description" value="false" />
    <arg name="moveit_controller_manager" value="ros_control" />
  </include>
</launch>
